name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install pnpm
      uses: pnpm/action-setup@v2
      with:
        version: 8
        
    - name: Install dependencies
      run: pnpm install
      
    - name: Lint code
      run: pnpm run lint
      
    - name: Build project
      run: pnpm run build
      
    - name: Start development server
      run: |
        pnpm run dev &
        sleep 10
        curl -f http://localhost:5173 || exit 1
      
    - name: Run Cypress tests
      uses: cypress-io/github-action@v6
      with:
        start: pnpm run dev
        wait-on: 'http://localhost:5173'
        wait-on-timeout: 120
        browser: chrome
        headless: true
        record: false
      env:
        CYPRESS_baseUrl: http://localhost:5173
        
    - name: Upload Cypress screenshots
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: cypress-screenshots
        path: cypress/screenshots
        
    - name: Upload Cypress videos
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: cypress-videos
        path: cypress/videos

  create-backup:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: success() && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        
    - name: Create backup branch
      run: |
        TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
        BACKUP_BRANCH="backup/main-${TIMESTAMP}"
        
        echo "Creating backup branch: $BACKUP_BRANCH"
        git checkout -b $BACKUP_BRANCH
        git push origin $BACKUP_BRANCH
        
        echo "✅ Backup branch created successfully: $BACKUP_BRANCH"
        echo "backup_branch=$BACKUP_BRANCH" >> $GITHUB_OUTPUT
        
    - name: Create release tag
      run: |
        TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
        TAG_NAME="release-${TIMESTAMP}"
        
        echo "Creating release tag: $TAG_NAME"
        git tag -a $TAG_NAME -m "Automated release after successful build and tests"
        git push origin $TAG_NAME
        
        echo "✅ Release tag created successfully: $TAG_NAME"
        
    - name: Comment on commit
      uses: actions/github-script@v7
      with:
        script: |
          const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
          const backupBranch = `backup/main-${timestamp}`;
          const releaseTag = `release-${timestamp}`;
          
          github.rest.repos.createCommitComment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            commit_sha: context.sha,
            body: `🎉 **Build and Tests Successful!**\n\n✅ All tests passed\n🔄 Backup branch created: \`${backupBranch}\`\n🏷️ Release tag created: \`${releaseTag}\`\n\nThis commit has been automatically backed up and tagged.`
          });

  notify-failure:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: failure()
    
    steps:
    - name: Comment on failure
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.repos.createCommitComment({
            owner: context.repo.owner,
            repo: context.repo.repo,
            commit_sha: context.sha,
            body: `❌ **Build or Tests Failed**\n\nThe CI/CD pipeline failed for this commit. Please check the workflow logs for details.\n\n- Build status: Failed\n- Tests status: Failed\n- No backup branch created`
          });