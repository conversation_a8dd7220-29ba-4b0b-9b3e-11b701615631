{"name": "sackobaqatar.com", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "copy-images": "node scripts/copy-event-images.sh", "generate-event-data": "node scripts/generate-complete-event-data.js", "optimize-images": "node scripts/optimize-images.js", "setup-images": "npm run copy-images && npm run generate-event-data && npm run optimize-images", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:run:chrome": "cypress run --browser chrome", "cypress:run:firefox": "cypress run --browser firefox", "cypress:run:edge": "cypress run --browser edge", "test:e2e": "cypress run", "test:e2e:headed": "cypress run --headed", "test:e2e:dev": "start-server-and-test dev http://localhost:5173 cypress:run", "test:accessibility": "cypress run --spec 'cypress/e2e/12-accessibility.cy.ts'", "test:performance": "cypress run --spec 'cypress/e2e/13-performance.cy.ts'", "test:integration": "cypress run --spec 'cypress/e2e/14-integration.cy.ts'", "test:responsive": "cypress run --spec 'cypress/e2e/11-responsive-design.cy.ts'"}, "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.47.10", "@types/react-lazy-load-image-component": "^1.6.4", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.13.1", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-country-flag": "^3.1.0", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-icons": "^5.4.0", "react-lazy-load-image-component": "^1.6.3", "react-masonry-css": "^1.0.16", "react-router-dom": "^7.0.2", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/mocha": "^10.0.10", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "axe-core": "^4.10.3", "cypress": "^14.4.1", "cypress-axe": "^1.6.0", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "postcss": "^8.4.49", "sharp": "^0.33.5", "start-server-and-test": "^2.0.3", "tailwindcss": "^3.4.16", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.14", "webpack": "^5.99.9"}}