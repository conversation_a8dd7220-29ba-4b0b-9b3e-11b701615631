@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=UnifrakturMaguntia&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-slate-900 text-gray-200;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply text-white;
  }
  
  /* Exception for event sections */
  .event-container,
  .event-container * {
    @apply bg-white text-gray-800;
  }
  
  .event-container h3 {
    @apply text-[#541D67];
  }
  
  .event-container p,
  .event-container span:not(.year-header *) {
    @apply text-gray-600;
  }
  
  .year-header,
  .year-header * {
    @apply bg-transparent text-white;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 transition-colors;
  }
  
  .btn-secondary {
    @apply bg-primary-400 text-white hover:bg-primary-500 transition-colors;
  }

  .section-gradient {
    @apply bg-gradient-to-br from-slate-900 via-slate-800 to-primary-300/5;
  }

  .hero-text {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                -2px -2px 4px rgba(0, 0, 0, 0.8),
                2px -2px 4px rgba(0, 0, 0, 0.8),
                -2px 2px 4px rgba(0, 0, 0, 0.8);
  }
}

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
