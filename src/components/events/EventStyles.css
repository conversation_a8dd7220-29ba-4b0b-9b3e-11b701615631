/* Override global dark theme for events section */
/* Using high specificity selectors and !important flags */

/* Reset styles for the events section to override dark theme */
.space-y-8 .event-container,
.space-y-8 .event-container *,
.space-y-8 .bg-white,
.space-y-8 .divide-y,
.space-y-8 [class*="bg-white"] {
  background-color: white !important;
}

/* Force text colors */
.space-y-8 .event-container h1,
.space-y-8 .event-container h2,
.space-y-8 .event-container h3,
.space-y-8 .event-container h4,
.space-y-8 .event-container h5,
.space-y-8 .event-container h6,
.space-y-8 .event-container p,
.space-y-8 .event-container span:not(.year-header *) {
  color: #333 !important;
}

/* Container styling */
.event-container {
  background-color: white !important;
  color: #333 !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  margin-bottom: 1rem !important;
  border: 1px solid #e5e7eb !important;
}

/* Mobile responsive improvements */
@media (max-width: 640px) {
  .event-container {
    margin-bottom: 0.75rem !important;
    border-radius: 0.375rem !important;
  }
  
  .year-header {
    padding: 0.75rem 1rem !important;
  }
  
  .year-header h2 {
    font-size: 1.125rem !important;
  }
}

/* Ensure consistent card heights in grid */
.grid > div {
  height: 100% !important;
}

/* Image container improvements */
.group.relative.aspect-square {
  min-height: 150px !important;
}

@media (min-width: 640px) {
  .group.relative.aspect-square {
    min-height: 200px !important;
  }
}

@media (min-width: 1024px) {
  .group.relative.aspect-square {
    min-height: 220px !important;
  }
}

/* Event title */
.event-title {
  color: #541D67 !important;
  font-weight: 600 !important;
  font-size: 1.125rem !important;
}

/* Event metadata */
.event-container .flex-1 .flex.items-center.space-x-4 span {
  color: #4B5563 !important;
}

/* Event description */
.event-description {
  color: #4B5563 !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  margin-top: 0.5rem !important;
}

/* Image overlay */
.event-image-overlay {
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.7), transparent) !important;
  border-top: 1px solid rgba(229, 231, 235, 0.5) !important;
}

/* Image text */
.event-image-text {
  color: #111827 !important;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5) !important;
}

/* Tags */
.event-tag {
  background-color: #F3F4F6 !important;
  color: #4B5563 !important;
  border: 1px solid #E5E7EB !important;
  display: inline-block !important;
}

/* Year header */
.year-header {
  background: linear-gradient(to right, #541D67, #B62D71) !important;
  padding: 1rem 1.5rem !important;
}

/* Year header text */
.year-header h2,
.year-header span {
  color: white !important;
  font-weight: 700 !important;
}

/* Icons */
.event-container svg {
  color: #4B5563 !important;
}

/* Force white background on parent containers */
.space-y-8 > div[class*="bg-white"] {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
}

/* Ensure the year group container has proper styling */
.space-y-8 > div {
  background-color: white !important;
  border-radius: 0.5rem !important;
  overflow: hidden !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}
